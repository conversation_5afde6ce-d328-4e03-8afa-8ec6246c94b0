// Cart page module
import { createHeader } from '../components/header.js'
import { createFooter } from '../components/footer.js'

export function renderCartPage() {
    const app = document.querySelector('#app')
    
    app.innerHTML = `
        ${createHeader()}
        
        <!-- Cart Page -->
        <div class="container py-4">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="/" data-route="/">Trang chủ</a>
                    </li>
                    <li class="breadcrumb-item active">Giỏ hàng</li>
                </ol>
            </nav>

            <div class="row">
                <!-- Cart Items -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-cart3 me-2"></i>
                                Giỏ hàng của bạn (3 sản phẩm)
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            ${createCartItems()}
                        </div>
                    </div>

                    <!-- Recommended Products -->
                    <div class="mt-4">
                        <h5 class="mb-3">Có thể bạn cũng thích</h5>
                        <div class="row g-3">
                            ${createRecommendedProducts()}
                        </div>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="col-lg-4">
                    <div class="card sticky-top" style="top: 100px;">
                        <div class="card-header">
                            <h6 class="mb-0">Tóm tắt đơn hàng</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Tạm tính:</span>
                                <span>555,000đ</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Phí vận chuyển:</span>
                                <span>25,000đ</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Giảm giá:</span>
                                <span class="text-success">-50,000đ</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between fw-bold fs-5">
                                <span>Tổng cộng:</span>
                                <span class="text-success">530,000đ</span>
                            </div>

                            <!-- Voucher -->
                            <div class="mt-3">
                                <label class="form-label">Mã giảm giá</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Nhập mã giảm giá">
                                    <button class="btn btn-outline-success" type="button">
                                        Áp dụng
                                    </button>
                                </div>
                            </div>

                            <!-- Checkout Button -->
                            <div class="d-grid mt-4">
                                <button class="btn btn-success btn-lg" data-route="/checkout">
                                    <i class="bi bi-credit-card me-2"></i>
                                    Thanh toán
                                </button>
                            </div>

                            <!-- Continue Shopping -->
                            <div class="text-center mt-3">
                                <a href="/categories" class="text-decoration-none" data-route="/categories">
                                    <i class="bi bi-arrow-left me-1"></i>
                                    Tiếp tục mua sắm
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        ${createFooter()}
    `
    
    initializeCart()
}

function createCartItems() {
    const cartItems = [
        {
            id: 1,
            name: 'Rau cải xanh hữu cơ',
            shop: 'Cô Lan Rau Sạch',
            market: 'Chợ Bến Thành',
            price: 25000,
            originalPrice: 30000,
            quantity: 2,
            image: 'https://via.placeholder.com/100x80/28a745/ffffff?text=Rau+Cải',
            inStock: true
        },
        {
            id: 2,
            name: 'Cá thu tươi nguyên con',
            shop: 'Bác Tám Hải Sản',
            market: 'Chợ Cầu Mống',
            price: 180000,
            originalPrice: null,
            quantity: 1,
            image: 'https://via.placeholder.com/100x80/17a2b8/ffffff?text=Cá+Thu',
            inStock: true
        },
        {
            id: 3,
            name: 'Tôm sú tươi size lớn',
            shop: 'Chị Hoa Hải Sản',
            market: 'Chợ Đồng Xuân',
            price: 350000,
            originalPrice: null,
            quantity: 1,
            image: 'https://via.placeholder.com/100x80/ffc107/ffffff?text=Tôm+Sú',
            inStock: false
        }
    ]
    
    return cartItems.map((item, index) => `
        <div class="cart-item p-4 ${index < cartItems.length - 1 ? 'border-bottom' : ''}">
            <div class="row align-items-center">
                <div class="col-md-1">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" ${item.inStock ? 'checked' : 'disabled'}>
                    </div>
                </div>
                <div class="col-md-2">
                    <img src="${item.image}" class="img-fluid rounded" alt="${item.name}">
                    ${!item.inStock ? '<span class="badge bg-danger position-absolute">Hết hàng</span>' : ''}
                </div>
                <div class="col-md-5">
                    <h6 class="mb-1">${item.name}</h6>
                    <p class="text-muted small mb-1">
                        <i class="bi bi-shop me-1"></i>${item.shop}
                    </p>
                    <p class="text-muted small mb-0">
                        <i class="bi bi-geo-alt me-1"></i>${item.market}
                    </p>
                </div>
                <div class="col-md-2">
                    <div class="text-success fw-bold">${item.price.toLocaleString()}đ</div>
                    ${item.originalPrice ? `
                        <div class="text-muted text-decoration-line-through small">
                            ${item.originalPrice.toLocaleString()}đ
                        </div>
                    ` : ''}
                </div>
                <div class="col-md-1">
                    <div class="input-group input-group-sm">
                        <button class="btn btn-outline-secondary quantity-btn" type="button" data-action="decrease" data-id="${item.id}">
                            <i class="bi bi-dash"></i>
                        </button>
                        <input type="number" class="form-control text-center quantity-input" value="${item.quantity}" min="1" data-id="${item.id}">
                        <button class="btn btn-outline-secondary quantity-btn" type="button" data-action="increase" data-id="${item.id}">
                            <i class="bi bi-plus"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-1 text-end">
                    <button class="btn btn-outline-danger btn-sm remove-item" data-id="${item.id}">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `).join('')
}

function createRecommendedProducts() {
    const products = [
        {
            name: 'Rau muống tươi',
            price: '15,000',
            image: 'https://via.placeholder.com/150x120/28a745/ffffff?text=Rau+Muống',
            shop: 'Cô Lan Rau Sạch'
        },
        {
            name: 'Cá basa phi lê',
            price: '120,000',
            image: 'https://via.placeholder.com/150x120/17a2b8/ffffff?text=Cá+Basa',
            shop: 'Bác Tám Hải Sản'
        },
        {
            name: 'Tôm thẻ tươi',
            price: '280,000',
            image: 'https://via.placeholder.com/150x120/ffc107/ffffff?text=Tôm+Thẻ',
            shop: 'Chị Hoa Hải Sản'
        }
    ]
    
    return products.map(product => `
        <div class="col-md-4">
            <div class="card recommended-product-card">
                <img src="${product.image}" class="card-img-top" alt="${product.name}">
                <div class="card-body p-3">
                    <h6 class="card-title small">${product.name}</h6>
                    <p class="text-muted small mb-2">${product.shop}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-success fw-bold">${product.price}đ</span>
                        <button class="btn btn-success btn-sm">
                            <i class="bi bi-plus"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('')
}

function initializeCart() {
    // Quantity controls
    document.querySelectorAll('.quantity-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.dataset.action
            const id = this.dataset.id
            const input = document.querySelector(`.quantity-input[data-id="${id}"]`)
            let currentValue = parseInt(input.value)
            
            if (action === 'increase') {
                input.value = currentValue + 1
            } else if (action === 'decrease' && currentValue > 1) {
                input.value = currentValue - 1
            }
            
            updateCartTotal()
        })
    })
    
    // Remove item
    document.querySelectorAll('.remove-item').forEach(btn => {
        btn.addEventListener('click', function() {
            if (confirm('Bạn có chắc muốn xóa sản phẩm này?')) {
                this.closest('.cart-item').remove()
                updateCartTotal()
            }
        })
    })
    
    // Quantity input change
    document.querySelectorAll('.quantity-input').forEach(input => {
        input.addEventListener('change', updateCartTotal)
    })
}

function updateCartTotal() {
    // This would normally calculate based on actual quantities and prices
    // For demo purposes, we'll just show a simple update
    console.log('Cart total updated')
}
