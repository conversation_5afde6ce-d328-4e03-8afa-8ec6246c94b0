// Checkout page module
import { createHeader } from '../components/header.js'
import { createFooter } from '../components/footer.js'

export function renderCheckoutPage() {
    const app = document.querySelector('#app')
    
    app.innerHTML = `
        ${createHeader()}
        
        <!-- Checkout Page -->
        <div class="container py-4">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="/" data-route="/">Trang chủ</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="/cart" data-route="/cart">Giỏ hàng</a>
                    </li>
                    <li class="breadcrumb-item active">Thanh toán</li>
                </ol>
            </nav>

            <!-- Checkout Steps -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-center">
                        <div class="checkout-steps d-flex align-items-center">
                            <div class="step active">
                                <div class="step-number">1</div>
                                <div class="step-label">Thông tin</div>
                            </div>
                            <div class="step-line"></div>
                            <div class="step">
                                <div class="step-number">2</div>
                                <div class="step-label">Thanh toán</div>
                            </div>
                            <div class="step-line"></div>
                            <div class="step">
                                <div class="step-number">3</div>
                                <div class="step-label">Hoàn thành</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Checkout Form -->
                <div class="col-lg-8">
                    <form id="checkoutForm">
                        <!-- Delivery Information -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-truck me-2"></i>
                                    Thông tin giao hàng
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">Họ và tên *</label>
                                        <input type="text" class="form-control" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Số điện thoại *</label>
                                        <input type="tel" class="form-control" required>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Địa chỉ giao hàng *</label>
                                    <input type="text" class="form-control" placeholder="Số nhà, tên đường" required>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label class="form-label">Tỉnh/Thành phố *</label>
                                        <select class="form-select" required>
                                            <option value="">Chọn tỉnh/thành</option>
                                            <option>TP. Hồ Chí Minh</option>
                                            <option>Hà Nội</option>
                                            <option>Đà Nẵng</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Quận/Huyện *</label>
                                        <select class="form-select" required>
                                            <option value="">Chọn quận/huyện</option>
                                            <option>Quận 1</option>
                                            <option>Quận 3</option>
                                            <option>Quận 5</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Phường/Xã *</label>
                                        <select class="form-select" required>
                                            <option value="">Chọn phường/xã</option>
                                            <option>Phường Bến Nghé</option>
                                            <option>Phường Bến Thành</option>
                                            <option>Phường Cô Giang</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Ghi chú</label>
                                    <textarea class="form-control" rows="2" placeholder="Ghi chú cho người giao hàng..."></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Delivery Options -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-clock me-2"></i>
                                    Phương thức giao hàng
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="delivery" id="standard" checked>
                                    <label class="form-check-label" for="standard">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <strong>Giao hàng tiêu chuẩn</strong>
                                                <div class="text-muted small">Giao trong 2-3 ngày</div>
                                            </div>
                                            <div class="text-success fw-bold">25,000đ</div>
                                        </div>
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="delivery" id="express">
                                    <label class="form-check-label" for="express">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <strong>Giao hàng nhanh</strong>
                                                <div class="text-muted small">Giao trong ngày</div>
                                            </div>
                                            <div class="text-success fw-bold">45,000đ</div>
                                        </div>
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="delivery" id="pickup">
                                    <label class="form-check-label" for="pickup">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <strong>Nhận tại chợ</strong>
                                                <div class="text-muted small">Tự đến lấy hàng</div>
                                            </div>
                                            <div class="text-success fw-bold">Miễn phí</div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Methods -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-credit-card me-2"></i>
                                    Phương thức thanh toán
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="payment" id="cod" checked>
                                    <label class="form-check-label" for="cod">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-cash fs-4 me-3 text-success"></i>
                                            <div>
                                                <strong>Thanh toán khi nhận hàng (COD)</strong>
                                                <div class="text-muted small">Thanh toán bằng tiền mặt khi nhận hàng</div>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="payment" id="momo">
                                    <label class="form-check-label" for="momo">
                                        <div class="d-flex align-items-center">
                                            <img src="https://via.placeholder.com/40x40/e91e63/ffffff?text=M" class="me-3 rounded">
                                            <div>
                                                <strong>Ví MoMo</strong>
                                                <div class="text-muted small">Thanh toán qua ví điện tử MoMo</div>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="payment" id="zalopay">
                                    <label class="form-check-label" for="zalopay">
                                        <div class="d-flex align-items-center">
                                            <img src="https://via.placeholder.com/40x40/0068ff/ffffff?text=Z" class="me-3 rounded">
                                            <div>
                                                <strong>ZaloPay</strong>
                                                <div class="text-muted small">Thanh toán qua ví điện tử ZaloPay</div>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="payment" id="bank">
                                    <label class="form-check-label" for="bank">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-bank fs-4 me-3 text-primary"></i>
                                            <div>
                                                <strong>Chuyển khoản ngân hàng</strong>
                                                <div class="text-muted small">Chuyển khoản qua ATM/Internet Banking</div>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Order Summary -->
                <div class="col-lg-4">
                    <div class="card sticky-top" style="top: 100px;">
                        <div class="card-header">
                            <h6 class="mb-0">Đơn hàng của bạn</h6>
                        </div>
                        <div class="card-body">
                            <!-- Order Items -->
                            <div class="order-items mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div class="d-flex align-items-center">
                                        <img src="https://via.placeholder.com/50x40/28a745/ffffff?text=RC" class="me-2 rounded">
                                        <div>
                                            <div class="small">Rau cải xanh hữu cơ</div>
                                            <div class="text-muted small">x2</div>
                                        </div>
                                    </div>
                                    <div class="text-success">50,000đ</div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div class="d-flex align-items-center">
                                        <img src="https://via.placeholder.com/50x40/17a2b8/ffffff?text=CT" class="me-2 rounded">
                                        <div>
                                            <div class="small">Cá thu tươi nguyên con</div>
                                            <div class="text-muted small">x1</div>
                                        </div>
                                    </div>
                                    <div class="text-success">180,000đ</div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div class="d-flex align-items-center">
                                        <img src="https://via.placeholder.com/50x40/ffc107/ffffff?text=TS" class="me-2 rounded">
                                        <div>
                                            <div class="small">Tôm sú tươi size lớn</div>
                                            <div class="text-muted small">x1</div>
                                        </div>
                                    </div>
                                    <div class="text-success">350,000đ</div>
                                </div>
                            </div>

                            <hr>

                            <!-- Price Breakdown -->
                            <div class="d-flex justify-content-between mb-2">
                                <span>Tạm tính:</span>
                                <span>580,000đ</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Phí vận chuyển:</span>
                                <span id="shippingFee">25,000đ</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Giảm giá:</span>
                                <span class="text-success">-50,000đ</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between fw-bold fs-5">
                                <span>Tổng cộng:</span>
                                <span class="text-success" id="totalAmount">555,000đ</span>
                            </div>

                            <!-- Place Order Button -->
                            <div class="d-grid mt-4">
                                <button type="submit" form="checkoutForm" class="btn btn-success btn-lg">
                                    <i class="bi bi-check-circle me-2"></i>
                                    Đặt hàng
                                </button>
                            </div>

                            <!-- Security Info -->
                            <div class="text-center mt-3">
                                <small class="text-muted">
                                    <i class="bi bi-shield-check text-success me-1"></i>
                                    Thông tin của bạn được bảo mật
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        ${createFooter()}
    `
    
    initializeCheckout()
}

function initializeCheckout() {
    // Update shipping fee based on delivery method
    document.querySelectorAll('input[name="delivery"]').forEach(radio => {
        radio.addEventListener('change', function() {
            const shippingFeeElement = document.getElementById('shippingFee')
            const totalAmountElement = document.getElementById('totalAmount')
            
            let shippingFee = 0
            if (this.id === 'standard') shippingFee = 25000
            else if (this.id === 'express') shippingFee = 45000
            else if (this.id === 'pickup') shippingFee = 0
            
            shippingFeeElement.textContent = shippingFee === 0 ? 'Miễn phí' : `${shippingFee.toLocaleString()}đ`
            
            // Update total (580000 - 50000 + shipping fee)
            const total = 530000 + shippingFee
            totalAmountElement.textContent = `${total.toLocaleString()}đ`
        })
    })
    
    // Form submission
    document.getElementById('checkoutForm').addEventListener('submit', function(e) {
        e.preventDefault()
        
        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]')
        const originalText = submitBtn.innerHTML
        
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Đang xử lý...'
        submitBtn.disabled = true
        
        // Simulate order processing
        setTimeout(() => {
            alert('Đặt hàng thành công! Mã đơn hàng: #DH001234')
            submitBtn.innerHTML = originalText
            submitBtn.disabled = false
        }, 2000)
    })
    
    // Add checkout steps CSS
    const style = document.createElement('style')
    style.textContent = `
        .checkout-steps {
            gap: 20px;
        }
        
        .step {
            text-align: center;
            color: #6c757d;
        }
        
        .step.active {
            color: #28a745;
        }
        
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            font-weight: bold;
        }
        
        .step.active .step-number {
            background: #28a745;
            color: white;
        }
        
        .step-line {
            width: 60px;
            height: 2px;
            background: #e9ecef;
            margin-top: 20px;
        }
        
        .step.active + .step-line {
            background: #28a745;
        }
    `
    document.head.appendChild(style)
}
