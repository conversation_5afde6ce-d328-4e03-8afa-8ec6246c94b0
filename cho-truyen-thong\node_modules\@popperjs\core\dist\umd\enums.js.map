{"version": 3, "file": "enums.js", "sources": ["../../src/enums.js"], "sourcesContent": ["// @flow\nexport const top: 'top' = 'top';\nexport const bottom: 'bottom' = 'bottom';\nexport const right: 'right' = 'right';\nexport const left: 'left' = 'left';\nexport const auto: 'auto' = 'auto';\nexport type BasePlacement =\n  | typeof top\n  | typeof bottom\n  | typeof right\n  | typeof left;\nexport const basePlacements: Array<BasePlacement> = [top, bottom, right, left];\n\nexport const start: 'start' = 'start';\nexport const end: 'end' = 'end';\nexport type Variation = typeof start | typeof end;\n\nexport const clippingParents: 'clippingParents' = 'clippingParents';\nexport const viewport: 'viewport' = 'viewport';\nexport type Boundary = Element | Array<Element> | typeof clippingParents;\nexport type RootBoundary = typeof viewport | 'document';\n\nexport const popper: 'popper' = 'popper';\nexport const reference: 'reference' = 'reference';\nexport type Context = typeof popper | typeof reference;\n\nexport type VariationPlacement =\n  | 'top-start'\n  | 'top-end'\n  | 'bottom-start'\n  | 'bottom-end'\n  | 'right-start'\n  | 'right-end'\n  | 'left-start'\n  | 'left-end';\nexport type AutoPlacement = 'auto' | 'auto-start' | 'auto-end';\nexport type ComputedPlacement = VariationPlacement | BasePlacement;\nexport type Placement = AutoPlacement | BasePlacement | VariationPlacement;\n\nexport const variationPlacements: Array<VariationPlacement> = basePlacements.reduce(\n  (acc: Array<VariationPlacement>, placement: BasePlacement) =>\n    acc.concat([(`${placement}-${start}`: any), (`${placement}-${end}`: any)]),\n  []\n);\nexport const placements: Array<Placement> = [...basePlacements, auto].reduce(\n  (\n    acc: Array<Placement>,\n    placement: BasePlacement | typeof auto\n  ): Array<Placement> =>\n    acc.concat([\n      placement,\n      (`${placement}-${start}`: any),\n      (`${placement}-${end}`: any),\n    ]),\n  []\n);\n\n// modifiers that need to read the DOM\nexport const beforeRead: 'beforeRead' = 'beforeRead';\nexport const read: 'read' = 'read';\nexport const afterRead: 'afterRead' = 'afterRead';\n// pure-logic modifiers\nexport const beforeMain: 'beforeMain' = 'beforeMain';\nexport const main: 'main' = 'main';\nexport const afterMain: 'afterMain' = 'afterMain';\n// modifier with the purpose to write to the DOM (or write into a framework state)\nexport const beforeWrite: 'beforeWrite' = 'beforeWrite';\nexport const write: 'write' = 'write';\nexport const afterWrite: 'afterWrite' = 'afterWrite';\nexport const modifierPhases: Array<ModifierPhases> = [\n  beforeRead,\n  read,\n  afterRead,\n  beforeMain,\n  main,\n  afterMain,\n  beforeWrite,\n  write,\n  afterWrite,\n];\n\nexport type ModifierPhases =\n  | typeof beforeRead\n  | typeof read\n  | typeof afterRead\n  | typeof beforeMain\n  | typeof main\n  | typeof afterMain\n  | typeof beforeWrite\n  | typeof write\n  | typeof afterWrite;\n"], "names": ["top", "bottom", "right", "left", "auto", "basePlacements", "start", "end", "clippingParents", "viewport", "popper", "reference", "variationPlacements", "reduce", "acc", "placement", "concat", "placements", "beforeRead", "read", "afterRead", "<PERSON><PERSON><PERSON>", "main", "<PERSON><PERSON><PERSON>", "beforeWrite", "write", "afterWrite", "modifierPhases"], "mappings": ";;;;;;;;;;MACaA,GAAU,GAAG;MACbC,MAAgB,GAAG;MACnBC,KAAc,GAAG;MACjBC,IAAY,GAAG;MACfC,IAAY,GAAG;MAMfC,cAAoC,GAAG,CAACL,GAAD,EAAMC,MAAN,EAAcC,KAAd,EAAqBC,IAArB;MAEvCG,KAAc,GAAG;MACjBC,GAAU,GAAG;MAGbC,eAAkC,GAAG;MACrCC,QAAoB,GAAG;MAIvBC,MAAgB,GAAG;MACnBC,SAAsB,GAAG;MAgBzBC,mBAA8C,gBAAGP,cAAc,CAACQ,MAAf,CAC5D,UAACC,GAAD,EAAiCC,SAAjC;EAAA,SACED,GAAG,CAACE,MAAJ,CAAW,CAAKD,SAAL,SAAkBT,KAAlB,EAAqCS,SAArC,SAAkDR,GAAlD,CAAX,CADF;EAAA,CAD4D,EAG5D,EAH4D;MAKjDU,UAA4B,gBAAG,UAAIZ,cAAJ,GAAoBD,IAApB,GAA0BS,MAA1B,CAC1C,UACEC,GADF,EAEEC,SAFF;EAAA,SAIED,GAAG,CAACE,MAAJ,CAAW,CACTD,SADS,EAELA,SAFK,SAEQT,KAFR,EAGLS,SAHK,SAGQR,GAHR,CAAX,CAJF;EAAA,CAD0C,EAU1C,EAV0C;;MAc/BW,UAAwB,GAAG;MAC3BC,IAAY,GAAG;MACfC,SAAsB,GAAG;;MAEzBC,UAAwB,GAAG;MAC3BC,IAAY,GAAG;MACfC,SAAsB,GAAG;;MAEzBC,WAA0B,GAAG;MAC7BC,KAAc,GAAG;MACjBC,UAAwB,GAAG;MAC3BC,cAAqC,GAAG,CACnDT,UADmD,EAEnDC,IAFmD,EAGnDC,SAHmD,EAInDC,UAJmD,EAKnDC,IALmD,EAMnDC,SANmD,EAOnDC,WAPmD,EAQnDC,KARmD,EASnDC,UATmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}