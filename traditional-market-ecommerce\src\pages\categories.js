// Categories page module
import { createHeader } from '../components/header.js'
import { createFooter } from '../components/footer.js'

export function renderCategoriesPage() {
    const app = document.querySelector('#app')
    
    app.innerHTML = `
        ${createHeader()}
        
        <!-- Categories Page -->
        <div class="container py-4">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="/" data-route="/">Trang chủ</a>
                    </li>
                    <li class="breadcrumb-item active">Danh mục sản phẩm</li>
                </ol>
            </nav>

            <div class="row">
                <!-- Sidebar Filters -->
                <div class="col-lg-3 col-md-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-funnel me-2"></i>Bộ lọc
                            </h6>
                        </div>
                        <div class="card-body">
                            <!-- Category Filter -->
                            <div class="mb-4">
                                <h6>Loại mặt hàng</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="rau-cu">
                                    <label class="form-check-label" for="rau-cu">
                                        Rau củ quả (234)
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="hai-san">
                                    <label class="form-check-label" for="hai-san">
                                        Hải sản tươi (156)
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="thit">
                                    <label class="form-check-label" for="thit">
                                        Thịt gia cầm (189)
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="do-kho">
                                    <label class="form-check-label" for="do-kho">
                                        Đồ khô (67)
                                    </label>
                                </div>
                            </div>

                            <!-- Market Filter -->
                            <div class="mb-4">
                                <h6>Chợ bán hàng</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="ben-thanh">
                                    <label class="form-check-label" for="ben-thanh">
                                        Chợ Bến Thành
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="tan-dinh">
                                    <label class="form-check-label" for="tan-dinh">
                                        Chợ Tân Định
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="dong-xuan">
                                    <label class="form-check-label" for="dong-xuan">
                                        Chợ Đồng Xuân
                                    </label>
                                </div>
                            </div>

                            <!-- Price Range -->
                            <div class="mb-4">
                                <h6>Khoảng giá</h6>
                                <div class="row g-2">
                                    <div class="col">
                                        <input type="number" class="form-control form-control-sm" placeholder="Từ">
                                    </div>
                                    <div class="col">
                                        <input type="number" class="form-control form-control-sm" placeholder="Đến">
                                    </div>
                                </div>
                            </div>

                            <!-- Delivery Options -->
                            <div class="mb-4">
                                <h6>Giao hàng</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="same-day">
                                    <label class="form-check-label" for="same-day">
                                        Giao trong ngày
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="free-ship">
                                    <label class="form-check-label" for="free-ship">
                                        Miễn phí ship
                                    </label>
                                </div>
                            </div>

                            <button class="btn btn-success w-100">Áp dụng bộ lọc</button>
                        </div>
                    </div>
                </div>

                <!-- Products Grid -->
                <div class="col-lg-9 col-md-8">
                    <!-- Sort and View Options -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <span class="text-muted">Hiển thị 1-20 trong 156 sản phẩm</span>
                        </div>
                        <div class="d-flex gap-3 align-items-center">
                            <select class="form-select form-select-sm" style="width: auto;">
                                <option>Sắp xếp theo</option>
                                <option>Giá thấp đến cao</option>
                                <option>Giá cao đến thấp</option>
                                <option>Mới nhất</option>
                                <option>Bán chạy nhất</option>
                            </select>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-secondary btn-sm active">
                                    <i class="bi bi-grid"></i>
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm">
                                    <i class="bi bi-list"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Products Grid -->
                    <div class="row g-4">
                        ${createProductCards()}
                    </div>

                    <!-- Pagination -->
                    <nav class="mt-5">
                        <ul class="pagination justify-content-center">
                            <li class="page-item disabled">
                                <span class="page-link">Trước</span>
                            </li>
                            <li class="page-item active">
                                <span class="page-link">1</span>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">2</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">3</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">Sau</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>

        ${createFooter()}
    `
    
    initializeCategoriesPage()
}

function createProductCards() {
    const products = [
        {
            id: 1,
            name: 'Rau cải xanh hữu cơ',
            price: '25,000',
            originalPrice: '30,000',
            image: 'https://via.placeholder.com/250x200/28a745/ffffff?text=Rau+Cải',
            shop: 'Cô Lan Rau Sạch',
            market: 'Chợ Bến Thành',
            rating: 4.8,
            sold: 156,
            discount: 17
        },
        {
            id: 2,
            name: 'Cá thu tươi nguyên con',
            price: '180,000',
            originalPrice: null,
            image: 'https://via.placeholder.com/250x200/17a2b8/ffffff?text=Cá+Thu',
            shop: 'Bác Tám Hải Sản',
            market: 'Chợ Cầu Mống',
            rating: 4.9,
            sold: 89,
            discount: 0
        },
        {
            id: 3,
            name: 'Thịt ba chỉ heo sạch',
            price: '120,000',
            originalPrice: '140,000',
            image: 'https://via.placeholder.com/250x200/dc3545/ffffff?text=Thịt+Heo',
            shop: 'Anh Minh Thịt Sạch',
            market: 'Chợ Tân Định',
            rating: 4.7,
            sold: 234,
            discount: 14
        },
        {
            id: 4,
            name: 'Tôm sú tươi size lớn',
            price: '350,000',
            originalPrice: null,
            image: 'https://via.placeholder.com/250x200/ffc107/ffffff?text=Tôm+Sú',
            shop: 'Chị Hoa Hải Sản',
            market: 'Chợ Đồng Xuân',
            rating: 4.6,
            sold: 67,
            discount: 0
        },
        {
            id: 5,
            name: 'Gạo ST25 thơm dẻo',
            price: '85,000',
            originalPrice: '95,000',
            image: 'https://via.placeholder.com/250x200/6c757d/ffffff?text=Gạo+ST25',
            shop: 'Cô Linh Gạo Sạch',
            market: 'Chợ Bến Thành',
            rating: 4.8,
            sold: 345,
            discount: 11
        },
        {
            id: 6,
            name: 'Trái cây nhập khẩu',
            price: '45,000',
            originalPrice: null,
            image: 'https://via.placeholder.com/250x200/28a745/ffffff?text=Trái+Cây',
            shop: 'Anh Đức Trái Cây',
            market: 'Chợ Tân Định',
            rating: 4.5,
            sold: 123,
            discount: 0
        }
    ]
    
    return products.map(product => `
        <div class="col-lg-4 col-md-6">
            <div class="card product-card h-100">
                <div class="position-relative">
                    <img src="${product.image}" class="card-img-top" alt="${product.name}">
                    ${product.discount > 0 ? `
                        <span class="badge bg-danger position-absolute top-0 start-0 m-2">
                            -${product.discount}%
                        </span>
                    ` : ''}
                    <div class="product-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center">
                        <button class="btn btn-success btn-sm me-2">
                            <i class="bi bi-cart-plus"></i>
                        </button>
                        <button class="btn btn-outline-light btn-sm">
                            <i class="bi bi-heart"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <h6 class="card-title">${product.name}</h6>
                    <div class="d-flex align-items-center mb-2">
                        <span class="text-warning me-1">
                            ${'★'.repeat(Math.floor(product.rating))}
                        </span>
                        <span class="text-muted small">(${product.sold})</span>
                    </div>
                    <div class="d-flex align-items-center justify-content-between mb-2">
                        <div>
                            <span class="text-success fw-bold">${product.price}đ</span>
                            ${product.originalPrice ? `
                                <span class="text-muted text-decoration-line-through ms-2 small">
                                    ${product.originalPrice}đ
                                </span>
                            ` : ''}
                        </div>
                    </div>
                    <p class="card-text small text-muted mb-2">
                        <i class="bi bi-shop me-1"></i>${product.shop}
                    </p>
                    <p class="card-text small text-muted">
                        <i class="bi bi-geo-alt me-1"></i>${product.market}
                    </p>
                </div>
            </div>
        </div>
    `).join('')
}

function initializeCategoriesPage() {
    // Add product card hover effects
    const style = document.createElement('style')
    style.textContent = `
        .product-card {
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: var(--primary-color);
        }
        
        .product-overlay {
            background: rgba(0,0,0,0.7);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .product-card:hover .product-overlay {
            opacity: 1;
        }
    `
    document.head.appendChild(style)
}
