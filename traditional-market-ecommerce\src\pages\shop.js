// Shop page module
import { createHeader } from '../components/header.js'
import { createFooter } from '../components/footer.js'

export function renderShopPage() {
    const app = document.querySelector('#app')
    
    app.innerHTML = `
        ${createHeader()}
        
        <!-- Shop Page -->
        <div class="container py-4">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="/" data-route="/">Trang chủ</a>
                    </li>
                    <li class="breadcrumb-item active">Gian hàng</li>
                </ol>
            </nav>

            <!-- Shop Header -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-2 text-center">
                            <img src="https://ejeqlsvnqlodlssvukmb.supabase.co/storage/v1/object/public/img.fiwpr/user/lan.png" 
                                 class="rounded-circle mb-3" alt="Cô Lan">
                        </div>
                        <div class="col-md-6">
                            <h3>Gian hàng Cô Lan</h3>
                            <p class="text-muted mb-2">
                                <i class="bi bi-geo-alt me-2"></i>Chợ Bến Thành, Quận 1, TP.HCM
                            </p>
                            <div class="d-flex align-items-center mb-2">
                                <span class="text-warning me-2">
                                    ★★★★★
                                </span>
                                <span class="text-muted">4.8 (234 đánh giá)</span>
                            </div>
                            <p class="mb-0">
                                Chuyên cung cấp rau củ quả tươi sạch, hải sản tươi sống. 
                                Kinh nghiệm hơn 15 năm tại chợ Bến Thành.
                            </p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="fw-bold text-success">156</div>
                                    <small class="text-muted">Sản phẩm</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-success">4.8</div>
                                    <small class="text-muted">Đánh giá</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-success">1.2k</div>
                                    <small class="text-muted">Theo dõi</small>
                                </div>
                            </div>
                            <div class="mt-3">
                                <button class="btn btn-success me-2">
                                    <i class="bi bi-plus-circle me-1"></i>Theo dõi
                                </button>
                                <button class="btn btn-outline-success">
                                    <i class="bi bi-chat-dots me-1"></i>Chat
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Shop Navigation -->
            <ul class="nav nav-tabs mb-4">
                <li class="nav-item">
                    <a class="nav-link active" href="#products" data-bs-toggle="tab">
                        Sản phẩm (156)
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#livestream" data-bs-toggle="tab">
                        Livestream
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#reviews" data-bs-toggle="tab">
                        Đánh giá (234)
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#info" data-bs-toggle="tab">
                        Thông tin
                    </a>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Products Tab -->
                <div class="tab-pane fade show active" id="products">
                    <div class="row g-4">
                        ${createShopProducts()}
                    </div>
                </div>

                <!-- Livestream Tab -->
                <div class="tab-pane fade" id="livestream">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-body text-center py-5">
                                    <i class="bi bi-broadcast display-1 text-muted mb-3"></i>
                                    <h5>Chưa có livestream nào</h5>
                                    <p class="text-muted">Cô Lan sẽ livestream vào 19:00 hôm nay</p>
                                    <button class="btn btn-success">
                                        <i class="bi bi-bell me-1"></i>Nhận thông báo
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Lịch livestream</h6>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="bg-success rounded-circle me-3" style="width: 10px; height: 10px;"></div>
                                        <div>
                                            <div class="fw-bold">Hôm nay 19:00</div>
                                            <small class="text-muted">Rau củ tươi sạch</small>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="bg-warning rounded-circle me-3" style="width: 10px; height: 10px;"></div>
                                        <div>
                                            <div class="fw-bold">Ngày mai 20:00</div>
                                            <small class="text-muted">Hải sản tươi sống</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reviews Tab -->
                <div class="tab-pane fade" id="reviews">
                    ${createReviews()}
                </div>

                <!-- Info Tab -->
                <div class="tab-pane fade" id="info">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Thông tin gian hàng</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <strong>Tên gian hàng:</strong> Gian hàng Cô Lan
                                    </div>
                                    <div class="mb-3">
                                        <strong>Chủ gian hàng:</strong> Nguyễn Thị Lan
                                    </div>
                                    <div class="mb-3">
                                        <strong>Địa chỉ:</strong> Sạp số 15, Chợ Bến Thành, Quận 1
                                    </div>
                                    <div class="mb-3">
                                        <strong>Điện thoại:</strong> 0901.234.567
                                    </div>
                                    <div class="mb-3">
                                        <strong>Kinh nghiệm:</strong> 15 năm
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Chính sách</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <i class="bi bi-truck text-success me-2"></i>
                                        <strong>Giao hàng:</strong> Trong ngày (bán kính 5km)
                                    </div>
                                    <div class="mb-3">
                                        <i class="bi bi-arrow-clockwise text-success me-2"></i>
                                        <strong>Đổi trả:</strong> Trong 2 giờ nếu không tươi
                                    </div>
                                    <div class="mb-3">
                                        <i class="bi bi-shield-check text-success me-2"></i>
                                        <strong>Bảo đảm:</strong> 100% tươi sạch
                                    </div>
                                    <div class="mb-3">
                                        <i class="bi bi-credit-card text-success me-2"></i>
                                        <strong>Thanh toán:</strong> Tiền mặt, chuyển khoản
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        ${createFooter()}
    `
}

function createShopProducts() {
    const products = [
        {
            name: 'Rau cải xanh hữu cơ',
            price: '25,000',
            image: 'https://via.placeholder.com/200x150/28a745/ffffff?text=Rau+Cải',
            sold: 156
        },
        {
            name: 'Cá thu tươi nguyên con',
            price: '180,000',
            image: 'https://via.placeholder.com/200x150/17a2b8/ffffff?text=Cá+Thu',
            sold: 89
        },
        {
            name: 'Tôm sú tươi size lớn',
            price: '350,000',
            image: 'https://via.placeholder.com/200x150/ffc107/ffffff?text=Tôm+Sú',
            sold: 67
        },
        {
            name: 'Rau muống tươi',
            price: '15,000',
            image: 'https://via.placeholder.com/200x150/28a745/ffffff?text=Rau+Muống',
            sold: 234
        }
    ]
    
    return products.map(product => `
        <div class="col-lg-3 col-md-4 col-sm-6">
            <div class="card product-card h-100">
                <img src="${product.image}" class="card-img-top" alt="${product.name}">
                <div class="card-body">
                    <h6 class="card-title">${product.name}</h6>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-success fw-bold">${product.price}đ</span>
                        <small class="text-muted">Đã bán ${product.sold}</small>
                    </div>
                    <button class="btn btn-success btn-sm w-100 mt-2">
                        <i class="bi bi-cart-plus me-1"></i>Thêm vào giỏ
                    </button>
                </div>
            </div>
        </div>
    `).join('')
}

function createReviews() {
    const reviews = [
        {
            name: 'Anh Minh',
            rating: 5,
            date: '2 ngày trước',
            comment: 'Rau rất tươi, giao hàng nhanh. Sẽ mua lại!',
            avatar: 'https://ejeqlsvnqlodlssvukmb.supabase.co/storage/v1/object/public/img.fiwpr/user/minh.png'
        },
        {
            name: 'Chị Hoa',
            rating: 5,
            date: '1 tuần trước',
            comment: 'Cô Lan bán hàng rất tận tình, hải sản tươi ngon.',
            avatar: 'https://ejeqlsvnqlodlssvukmb.supabase.co/storage/v1/object/public/img.fiwpr/user/hoa.png'
        },
        {
            name: 'Anh Đức',
            rating: 4,
            date: '2 tuần trước',
            comment: 'Chất lượng tốt, giá cả hợp lý.',
            avatar: 'https://ejeqlsvnqlodlssvukmb.supabase.co/storage/v1/object/public/img.fiwpr//TanDinh.png'
        }
    ]
    
    return reviews.map(review => `
        <div class="card mb-3">
            <div class="card-body">
                <div class="d-flex align-items-start">
                    <img src="${review.avatar}" class="rounded-circle me-3" alt="${review.name}">
                    <div class="flex-grow-1">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">${review.name}</h6>
                            <small class="text-muted">${review.date}</small>
                        </div>
                        <div class="text-warning mb-2">
                            ${'★'.repeat(review.rating)}${'☆'.repeat(5-review.rating)}
                        </div>
                        <p class="mb-0">${review.comment}</p>
                    </div>
                </div>
            </div>
        </div>
    `).join('')
}
