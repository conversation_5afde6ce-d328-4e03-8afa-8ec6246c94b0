/* Custom styles for Traditional Market E-commerce */
:root {
  --primary-color: #28a745;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #fff;
  margin: 0;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header Styles */
.navbar-brand {
  font-size: 1.8rem !important;
  font-weight: 700;
}

.hero-banner {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  min-height: 500px;
  display: flex;
  align-items: center;
}

/* Category Cards */
.category-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  border-color: var(--primary-color);
}

.category-image-container {
  position: relative;
  display: inline-block;
}

.category-image {
  border: 3px solid #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.category-card:hover .category-image {
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* Livestream Cards */
.livestream-card {
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.livestream-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Shop Cards */
.shop-card {
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}

.shop-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  border-color: var(--primary-color);
}

.shop-card img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border: 2px solid #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Promotion Cards */
.promotion-card {
  transition: all 0.3s ease;
}

.promotion-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* Search Bar */
.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

/* Buttons */
.btn-success {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-success:hover {
  background-color: #218838;
  border-color: #1e7e34;
}

.btn-outline-success {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-success:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-banner {
    min-height: 400px;
    text-align: center;
  }

  .hero-banner .display-4 {
    font-size: 2rem;
  }

  .category-card {
    margin-bottom: 1rem;
  }
}

/* Loading Animation */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(40, 167, 69, 0.3);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Utility Classes */
.text-success {
  color: var(--primary-color) !important;
}

.bg-success {
  background-color: var(--primary-color) !important;
}

.border-success {
  border-color: var(--primary-color) !important;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #218838;
}
