// Promotions page module
import { createHeader } from '../components/header.js'
import { createFooter } from '../components/footer.js'

export function renderPromotionsPage() {
    const app = document.querySelector('#app')
    
    app.innerHTML = `
        ${createHeader()}
        
        <!-- Promotions Page -->
        <div class="container py-4">
            <!-- Page Header -->
            <div class="text-center mb-5">
                <h1 class="display-5 fw-bold text-success mb-3">
                    <i class="bi bi-gift me-3"></i>Ưu đãi & Khuyến mãi
                </h1>
                <p class="lead text-muted">
                    Tiết kiệm hơn với các voucher và chương trình khuyến mãi hấp dẫn
                </p>
            </div>

            <!-- Featured Promotions -->
            <div class="mb-5">
                <h3 class="mb-4">Ưu đãi nổi bật</h3>
                <div class="row g-4">
                    ${createFeaturedPromotions()}
                </div>
            </div>

            <!-- Voucher Collection -->
            <div class="mb-5">
                <h3 class="mb-4">Kho voucher</h3>
                <div class="row g-3">
                    ${createVoucherCollection()}
                </div>
            </div>

            <!-- Flash Sale -->
            <div class="mb-5">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h3 class="mb-3">
                                    <i class="bi bi-lightning-fill me-2"></i>
                                    Flash Sale - Giảm đến 70%
                                </h3>
                                <p class="mb-3">Chỉ trong hôm nay! Hàng ngàn sản phẩm giảm giá sốc</p>
                                <div class="d-flex align-items-center mb-3">
                                    <span class="me-3">Kết thúc sau:</span>
                                    <div class="d-flex gap-2">
                                        <span class="badge bg-light text-dark px-3 py-2">12</span>
                                        <span class="text-white">:</span>
                                        <span class="badge bg-light text-dark px-3 py-2">34</span>
                                        <span class="text-white">:</span>
                                        <span class="badge bg-light text-dark px-3 py-2">56</span>
                                    </div>
                                </div>
                                <button class="btn btn-light btn-lg">
                                    Mua ngay
                                </button>
                            </div>
                            <div class="col-md-6 text-center">
                                <img src="https://via.placeholder.com/300x200/ffffff/dc3545?text=FLASH+SALE" 
                                     class="img-fluid rounded" alt="Flash Sale">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loyalty Program -->
            <div class="mb-5">
                <h3 class="mb-4">Chương trình thành viên</h3>
                <div class="row g-4">
                    ${createLoyaltyProgram()}
                </div>
            </div>

            <!-- Referral Program -->
            <div class="mb-5">
                <div class="card bg-success text-white">
                    <div class="card-body text-center py-5">
                        <h3 class="mb-3">
                            <i class="bi bi-people-fill me-2"></i>
                            Giới thiệu bạn bè - Nhận ngay 100K
                        </h3>
                        <p class="mb-4">
                            Mời bạn bè tham gia ChợOnline, cả hai đều nhận voucher 100.000đ
                        </p>
                        <div class="row justify-content-center">
                            <div class="col-md-6">
                                <div class="input-group mb-3">
                                    <input type="text" class="form-control" 
                                           value="https://choponline.vn/ref/ABC123" readonly>
                                    <button class="btn btn-light" type="button">
                                        <i class="bi bi-copy"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <button class="btn btn-light btn-lg">
                            <i class="bi bi-share me-2"></i>Chia sẻ ngay
                        </button>
                    </div>
                </div>
            </div>
        </div>

        ${createFooter()}
    `
}

function createFeaturedPromotions() {
    const promotions = [
        {
            title: 'Giảm 100K cho đơn đầu tiên',
            description: 'Áp dụng cho khách hàng mới, đơn hàng từ 300K',
            code: 'WELCOME100',
            discount: '100K',
            color: 'danger',
            image: 'https://via.placeholder.com/400x200/dc3545/ffffff?text=WELCOME+100K'
        },
        {
            title: 'Freeship toàn quốc',
            description: 'Miễn phí giao hàng cho đơn từ 200K',
            code: 'FREESHIP200',
            discount: 'FREE',
            color: 'success',
            image: 'https://via.placeholder.com/400x200/28a745/ffffff?text=FREE+SHIP'
        },
        {
            title: 'Livestream Sale 50%',
            description: 'Giảm 50% khi mua trong livestream',
            code: 'LIVE50',
            discount: '50%',
            color: 'warning',
            image: 'https://via.placeholder.com/400x200/ffc107/ffffff?text=LIVE+50%'
        }
    ]
    
    return promotions.map(promo => `
        <div class="col-lg-4 col-md-6">
            <div class="card promotion-featured-card border-${promo.color}">
                <img src="${promo.image}" class="card-img-top" alt="${promo.title}">
                <div class="card-body">
                    <h5 class="card-title text-${promo.color}">${promo.title}</h5>
                    <p class="card-text">${promo.description}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <code class="bg-light px-3 py-2 rounded">${promo.code}</code>
                        <button class="btn btn-${promo.color} btn-sm copy-code" data-code="${promo.code}">
                            Sao chép
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('')
}

function createVoucherCollection() {
    const vouchers = [
        { title: 'Giảm 50K', min: '200K', code: 'SAVE50', type: 'Tổng hợp', color: 'primary' },
        { title: 'Giảm 30K', min: '150K', code: 'SAVE30', type: 'Rau củ', color: 'success' },
        { title: 'Giảm 100K', min: '500K', code: 'SAVE100', type: 'Hải sản', color: 'info' },
        { title: 'Giảm 20K', min: '100K', code: 'SAVE20', type: 'Thịt', color: 'danger' },
        { title: 'Giảm 15K', min: '80K', code: 'SAVE15', type: 'Đồ khô', color: 'warning' },
        { title: 'Giảm 25K', min: '120K', code: 'SAVE25', type: 'Trái cây', color: 'secondary' }
    ]
    
    return vouchers.map(voucher => `
        <div class="col-lg-4 col-md-6">
            <div class="card voucher-card border-${voucher.color}">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-4 text-center">
                            <div class="bg-${voucher.color} bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center" 
                                 style="width: 60px; height: 60px;">
                                <span class="fw-bold text-${voucher.color}">${voucher.title}</span>
                            </div>
                        </div>
                        <div class="col-8">
                            <h6 class="mb-1">${voucher.title}</h6>
                            <p class="text-muted small mb-2">Đơn tối thiểu ${voucher.min}</p>
                            <span class="badge bg-${voucher.color} bg-opacity-10 text-${voucher.color} mb-2">
                                ${voucher.type}
                            </span>
                            <div class="d-flex justify-content-between align-items-center">
                                <code class="small">${voucher.code}</code>
                                <button class="btn btn-${voucher.color} btn-sm copy-code" data-code="${voucher.code}">
                                    Lưu
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `).join('')
}

function createLoyaltyProgram() {
    const tiers = [
        {
            name: 'Đồng',
            requirement: '0 - 1M',
            benefits: ['Tích điểm 1%', 'Sinh nhật giảm 5%'],
            color: 'warning',
            icon: 'bi-award'
        },
        {
            name: 'Bạc',
            requirement: '1M - 5M',
            benefits: ['Tích điểm 2%', 'Freeship 1 lần/tháng', 'Sinh nhật giảm 10%'],
            color: 'secondary',
            icon: 'bi-award-fill'
        },
        {
            name: 'Vàng',
            requirement: '5M - 10M',
            benefits: ['Tích điểm 3%', 'Freeship không giới hạn', 'Ưu tiên hỗ trợ'],
            color: 'warning',
            icon: 'bi-gem'
        },
        {
            name: 'Kim cương',
            requirement: 'Trên 10M',
            benefits: ['Tích điểm 5%', 'Tất cả ưu đãi', 'Quà tặng độc quyền'],
            color: 'primary',
            icon: 'bi-diamond-fill'
        }
    ]
    
    return tiers.map(tier => `
        <div class="col-lg-3 col-md-6">
            <div class="card loyalty-card text-center">
                <div class="card-body">
                    <div class="bg-${tier.color} bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 60px; height: 60px;">
                        <i class="bi ${tier.icon} fs-3 text-${tier.color}"></i>
                    </div>
                    <h5 class="text-${tier.color}">${tier.name}</h5>
                    <p class="text-muted small mb-3">${tier.requirement}</p>
                    <ul class="list-unstyled text-start">
                        ${tier.benefits.map(benefit => `
                            <li class="mb-1">
                                <i class="bi bi-check text-${tier.color} me-2"></i>
                                ${benefit}
                            </li>
                        `).join('')}
                    </ul>
                </div>
            </div>
        </div>
    `).join('')
}
