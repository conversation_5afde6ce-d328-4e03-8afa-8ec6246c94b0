// Simple router for single page application
export function initRouter() {
    // Handle navigation
    window.addEventListener('popstate', handleRoute)
    
    // Handle initial load
    handleRoute()
    
    // Handle navigation clicks
    document.addEventListener('click', function(e) {
        if (e.target.matches('[data-route]')) {
            e.preventDefault()
            const route = e.target.getAttribute('data-route')
            navigateTo(route)
        }
    })
}

export function navigateTo(route) {
    history.pushState(null, null, route)
    handleRoute()
}

function handleRoute() {
    const path = window.location.pathname
    const app = document.querySelector('#app')
    
    // Clear current content
    app.innerHTML = ''
    
    // Route to appropriate page
    switch(path) {
        case '/':
        case '/home':
            loadHomePage()
            break
        case '/categories':
            loadCategoriesPage()
            break
        case '/shop':
            loadShopPage()
            break
        case '/livestream':
            loadLivestreamPage()
            break
        case '/merchant-register':
            loadMerchantRegisterPage()
            break
        case '/promotions':
            loadPromotionsPage()
            break
        case '/community':
            loadCommunityPage()
            break
        case '/cart':
            loadCartPage()
            break
        case '/checkout':
            loadCheckoutPage()
            break
        case '/dashboard':
            loadDashboardPage()
            break
        default:
            load404Page()
    }
}

// Page loading functions (will be implemented)
function loadHomePage() {
    import('./pages/home.js').then(module => {
        module.renderHomePage()
    })
}

function loadCategoriesPage() {
    import('./pages/categories.js').then(module => {
        module.renderCategoriesPage()
    })
}

function loadShopPage() {
    import('./pages/shop.js').then(module => {
        module.renderShopPage()
    })
}

function loadLivestreamPage() {
    import('./pages/livestream.js').then(module => {
        module.renderLivestreamPage()
    })
}

function loadMerchantRegisterPage() {
    import('./pages/merchant-register.js').then(module => {
        module.renderMerchantRegisterPage()
    })
}

function loadPromotionsPage() {
    import('./pages/promotions.js').then(module => {
        module.renderPromotionsPage()
    })
}

function loadCommunityPage() {
    import('./pages/community.js').then(module => {
        module.renderCommunityPage()
    })
}

function loadCartPage() {
    import('./pages/cart.js').then(module => {
        module.renderCartPage()
    })
}

function loadCheckoutPage() {
    import('./pages/checkout.js').then(module => {
        module.renderCheckoutPage()
    })
}

function loadDashboardPage() {
    import('./pages/dashboard.js').then(module => {
        module.renderDashboardPage()
    })
}

function load404Page() {
    document.querySelector('#app').innerHTML = `
        <div class="container text-center mt-5">
            <h1>404 - Trang không tồn tại</h1>
            <p>Xin lỗi, trang bạn tìm kiếm không tồn tại.</p>
            <a href="/" class="btn btn-primary" data-route="/">Về trang chủ</a>
        </div>
    `
}
