// Livestream page module
import { createHeader } from '../components/header.js'
import { createFooter } from '../components/footer.js'

export function renderLivestreamPage() {
    const app = document.querySelector('#app')
    
    app.innerHTML = `
        ${createHeader()}
        
        <!-- Livestream Page -->
        <div class="container py-4">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="bi bi-broadcast text-danger me-2"></i>
                    Livestream bán hàng
                </h2>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-success">
                        <i class="bi bi-calendar me-1"></i>Lịch livestream
                    </button>
                    <button class="btn btn-success">
                        <i class="bi bi-plus-circle me-1"></i>Tạo livestream
                    </button>
                </div>
            </div>

            <!-- Live Now Section -->
            <div class="mb-5">
                <h4 class="mb-3">
                    <span class="badge bg-danger me-2">LIVE</span>
                    Đang phát trực tiếp
                </h4>
                <div class="row g-4">
                    ${createLiveStreams()}
                </div>
            </div>

            <!-- Upcoming Streams -->
            <div class="mb-5">
                <h4 class="mb-3">Sắp diễn ra</h4>
                <div class="row g-4">
                    ${createUpcomingStreams()}
                </div>
            </div>

            <!-- Past Streams -->
            <div class="mb-5">
                <h4 class="mb-3">Video đã lưu</h4>
                <div class="row g-4">
                    ${createPastStreams()}
                </div>
            </div>
        </div>

        ${createFooter()}
    `
}

function createLiveStreams() {
    const liveStreams = [
        {
            title: 'Hải sản tươi sống - Flash Sale 50%',
            merchant: 'Cô Lan Hải Sản',
            market: 'Chợ Bến Thành',
            viewers: '2,345',
            thumbnail: 'https://via.placeholder.com/400x250/dc3545/ffffff?text=LIVE+Hải+Sản',
            duration: '45:23'
        },
        {
            title: 'Rau củ organic - Giảm giá sốc',
            merchant: 'Anh Minh Rau Sạch',
            market: 'Chợ Tân Định',
            viewers: '1,567',
            thumbnail: 'https://via.placeholder.com/400x250/28a745/ffffff?text=LIVE+Rau+Củ',
            duration: '32:15'
        }
    ]
    
    return liveStreams.map(stream => `
        <div class="col-lg-6">
            <div class="card livestream-card">
                <div class="position-relative">
                    <img src="${stream.thumbnail}" class="card-img-top" alt="${stream.title}">
                    <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center livestream-overlay">
                        <button class="btn btn-light btn-lg rounded-circle">
                            <i class="bi bi-play-fill fs-3"></i>
                        </button>
                    </div>
                    <span class="badge bg-danger position-absolute top-0 start-0 m-2">
                        <i class="bi bi-broadcast"></i> LIVE
                    </span>
                    <span class="badge bg-dark bg-opacity-75 position-absolute top-0 end-0 m-2">
                        <i class="bi bi-eye"></i> ${stream.viewers}
                    </span>
                    <span class="badge bg-dark bg-opacity-75 position-absolute bottom-0 end-0 m-2">
                        ${stream.duration}
                    </span>
                </div>
                <div class="card-body">
                    <h5 class="card-title">${stream.title}</h5>
                    <p class="card-text">
                        <i class="bi bi-shop me-1"></i>${stream.merchant}
                        <br>
                        <i class="bi bi-geo-alt me-1"></i>${stream.market}
                    </p>
                    <div class="d-flex gap-2">
                        <button class="btn btn-danger flex-grow-1">
                            <i class="bi bi-play-circle me-1"></i>Xem ngay
                        </button>
                        <button class="btn btn-outline-success">
                            <i class="bi bi-heart"></i>
                        </button>
                        <button class="btn btn-outline-success">
                            <i class="bi bi-share"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('')
}

function createUpcomingStreams() {
    const upcomingStreams = [
        {
            title: 'Trái cây nhập khẩu - Khuyến mãi cuối tuần',
            merchant: 'Chị Hoa Trái Cây',
            market: 'Chợ Đồng Xuân',
            scheduledTime: 'Hôm nay 19:00',
            thumbnail: 'https://via.placeholder.com/300x200/ffc107/ffffff?text=Trái+Cây',
            followers: '456'
        },
        {
            title: 'Thịt heo sạch - Giá tốt nhất tuần',
            merchant: 'Bác Tám Thịt Sạch',
            market: 'Chợ Cầu Mống',
            scheduledTime: 'Ngày mai 20:00',
            thumbnail: 'https://via.placeholder.com/300x200/dc3545/ffffff?text=Thịt+Heo',
            followers: '789'
        },
        {
            title: 'Đồ khô đặc sản - Tết đến rồi',
            merchant: 'Cô Linh Đồ Khô',
            market: 'Chợ Bến Thành',
            scheduledTime: 'Thứ 7 18:00',
            thumbnail: 'https://via.placeholder.com/300x200/6c757d/ffffff?text=Đồ+Khô',
            followers: '234'
        }
    ]
    
    return upcomingStreams.map(stream => `
        <div class="col-lg-4 col-md-6">
            <div class="card upcoming-stream-card">
                <div class="position-relative">
                    <img src="${stream.thumbnail}" class="card-img-top" alt="${stream.title}">
                    <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center upcoming-overlay">
                        <div class="text-center text-white">
                            <i class="bi bi-clock display-4 mb-2"></i>
                            <div class="fw-bold">${stream.scheduledTime}</div>
                        </div>
                    </div>
                    <span class="badge bg-warning position-absolute top-0 start-0 m-2">
                        <i class="bi bi-calendar"></i> Sắp diễn ra
                    </span>
                </div>
                <div class="card-body">
                    <h6 class="card-title">${stream.title}</h6>
                    <p class="card-text small">
                        <i class="bi bi-shop me-1"></i>${stream.merchant}
                        <br>
                        <i class="bi bi-geo-alt me-1"></i>${stream.market}
                    </p>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="bi bi-people me-1"></i>${stream.followers} theo dõi
                        </small>
                        <button class="btn btn-outline-success btn-sm">
                            <i class="bi bi-bell me-1"></i>Nhắc nhở
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('')
}

function createPastStreams() {
    const pastStreams = [
        {
            title: 'Hướng dẫn chọn hải sản tươi',
            merchant: 'Cô Lan Hải Sản',
            views: '12,345',
            duration: '1:23:45',
            date: '2 ngày trước',
            thumbnail: 'https://via.placeholder.com/300x200/17a2b8/ffffff?text=Hải+Sản+Tips'
        },
        {
            title: 'Rau củ organic - Bí quyết bảo quản',
            merchant: 'Anh Minh Rau Sạch',
            views: '8,567',
            duration: '45:32',
            date: '1 tuần trước',
            thumbnail: 'https://via.placeholder.com/300x200/28a745/ffffff?text=Rau+Tips'
        },
        {
            title: 'Món ngon từ thịt heo - Livestream nấu ăn',
            merchant: 'Bác Tám Thịt Sạch',
            views: '15,678',
            duration: '2:15:20',
            date: '2 tuần trước',
            thumbnail: 'https://via.placeholder.com/300x200/dc3545/ffffff?text=Nấu+Ăn'
        }
    ]
    
    return pastStreams.map(stream => `
        <div class="col-lg-4 col-md-6">
            <div class="card past-stream-card">
                <div class="position-relative">
                    <img src="${stream.thumbnail}" class="card-img-top" alt="${stream.title}">
                    <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center past-overlay">
                        <button class="btn btn-light btn-lg rounded-circle">
                            <i class="bi bi-play-fill fs-3"></i>
                        </button>
                    </div>
                    <span class="badge bg-dark bg-opacity-75 position-absolute bottom-0 end-0 m-2">
                        ${stream.duration}
                    </span>
                </div>
                <div class="card-body">
                    <h6 class="card-title">${stream.title}</h6>
                    <p class="card-text small">
                        <i class="bi bi-shop me-1"></i>${stream.merchant}
                    </p>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="bi bi-eye me-1"></i>${stream.views} lượt xem
                        </small>
                        <small class="text-muted">${stream.date}</small>
                    </div>
                </div>
            </div>
        </div>
    `).join('')
}
