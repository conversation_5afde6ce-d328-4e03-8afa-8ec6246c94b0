// Dashboard page module
import { createHeader } from '../components/header.js'
import { createFooter } from '../components/footer.js'

export function renderDashboardPage() {
    const app = document.querySelector('#app')
    
    app.innerHTML = `
        ${createHeader()}
        
        <!-- Dashboard Page -->
        <div class="container py-4">
            <div class="row">
                <!-- Sidebar -->
                <div class="col-lg-3 col-md-4 mb-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <img src="https://via.placeholder.com/100x100/28a745/ffffff?text=User" 
                                 class="rounded-circle mb-3" alt="User Avatar">
                            <h5>Nguyễn Văn A</h5>
                            <p class="text-muted"><EMAIL></p>
                            <span class="badge bg-warning">Thành viên Vàng</span>
                        </div>
                    </div>
                    
                    <div class="list-group mt-3">
                        <a href="#profile" class="list-group-item list-group-item-action active" data-tab="profile">
                            <i class="bi bi-person me-2"></i>Thông tin cá nhân
                        </a>
                        <a href="#orders" class="list-group-item list-group-item-action" data-tab="orders">
                            <i class="bi bi-box-seam me-2"></i>Đơn hàng của tôi
                        </a>
                        <a href="#favorites" class="list-group-item list-group-item-action" data-tab="favorites">
                            <i class="bi bi-heart me-2"></i>Yêu thích
                        </a>
                        <a href="#addresses" class="list-group-item list-group-item-action" data-tab="addresses">
                            <i class="bi bi-geo-alt me-2"></i>Sổ địa chỉ
                        </a>
                        <a href="#notifications" class="list-group-item list-group-item-action" data-tab="notifications">
                            <i class="bi bi-bell me-2"></i>Thông báo
                        </a>
                        <a href="#settings" class="list-group-item list-group-item-action" data-tab="settings">
                            <i class="bi bi-gear me-2"></i>Cài đặt
                        </a>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="col-lg-9 col-md-8">
                    <!-- Profile Tab -->
                    <div class="tab-content active" id="profile-content">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Thông tin cá nhân</h5>
                            </div>
                            <div class="card-body">
                                <form>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label">Họ và tên</label>
                                            <input type="text" class="form-control" value="Nguyễn Văn A">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Email</label>
                                            <input type="email" class="form-control" value="<EMAIL>">
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label">Số điện thoại</label>
                                            <input type="tel" class="form-control" value="0901234567">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Ngày sinh</label>
                                            <input type="date" class="form-control" value="1990-01-01">
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Địa chỉ</label>
                                        <input type="text" class="form-control" value="123 Đường ABC, Quận 1, TP.HCM">
                                    </div>
                                    <button type="submit" class="btn btn-success">Cập nhật thông tin</button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Orders Tab -->
                    <div class="tab-content" id="orders-content">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Đơn hàng của tôi</h5>
                            </div>
                            <div class="card-body">
                                <!-- Order Status Tabs -->
                                <ul class="nav nav-pills mb-4">
                                    <li class="nav-item">
                                        <a class="nav-link active" href="#all-orders">Tất cả</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#pending">Chờ xác nhận</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#shipping">Đang giao</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#completed">Hoàn thành</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#cancelled">Đã hủy</a>
                                    </li>
                                </ul>

                                <!-- Orders List -->
                                ${createOrdersList()}
                            </div>
                        </div>
                    </div>

                    <!-- Favorites Tab -->
                    <div class="tab-content" id="favorites-content">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Sản phẩm yêu thích</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    ${createFavoriteProducts()}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Addresses Tab -->
                    <div class="tab-content" id="addresses-content">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Sổ địa chỉ</h5>
                                <button class="btn btn-success btn-sm">
                                    <i class="bi bi-plus me-1"></i>Thêm địa chỉ
                                </button>
                            </div>
                            <div class="card-body">
                                ${createAddressList()}
                            </div>
                        </div>
                    </div>

                    <!-- Notifications Tab -->
                    <div class="tab-content" id="notifications-content">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Thông báo</h5>
                            </div>
                            <div class="card-body">
                                ${createNotificationsList()}
                            </div>
                        </div>
                    </div>

                    <!-- Settings Tab -->
                    <div class="tab-content" id="settings-content">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Cài đặt tài khoản</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-4">
                                    <h6>Thông báo</h6>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="emailNotif" checked>
                                        <label class="form-check-label" for="emailNotif">
                                            Nhận thông báo qua email
                                        </label>
                                    </div>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="smsNotif">
                                        <label class="form-check-label" for="smsNotif">
                                            Nhận thông báo qua SMS
                                        </label>
                                    </div>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="promoNotif" checked>
                                        <label class="form-check-label" for="promoNotif">
                                            Nhận thông báo khuyến mãi
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-4">
                                    <h6>Bảo mật</h6>
                                    <button class="btn btn-outline-primary me-2">Đổi mật khẩu</button>
                                    <button class="btn btn-outline-secondary">Xác thực 2 bước</button>
                                </div>
                                
                                <div class="mb-4">
                                    <h6>Tài khoản</h6>
                                    <button class="btn btn-outline-danger">Xóa tài khoản</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        ${createFooter()}
    `
    
    initializeDashboard()
}

function createOrdersList() {
    const orders = [
        {
            id: 'DH001234',
            date: '15/06/2024',
            status: 'completed',
            statusText: 'Hoàn thành',
            total: '530,000đ',
            items: [
                { name: 'Rau cải xanh hữu cơ', quantity: 2, image: 'https://via.placeholder.com/60x50/28a745/ffffff?text=RC' },
                { name: 'Cá thu tươi', quantity: 1, image: 'https://via.placeholder.com/60x50/17a2b8/ffffff?text=CT' }
            ]
        },
        {
            id: 'DH001235',
            date: '18/06/2024',
            status: 'shipping',
            statusText: 'Đang giao',
            total: '280,000đ',
            items: [
                { name: 'Tôm sú tươi', quantity: 1, image: 'https://via.placeholder.com/60x50/ffc107/ffffff?text=TS' }
            ]
        }
    ]
    
    return orders.map(order => `
        <div class="border rounded p-3 mb-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <h6 class="mb-1">Đơn hàng #${order.id}</h6>
                    <small class="text-muted">${order.date}</small>
                </div>
                <span class="badge ${order.status === 'completed' ? 'bg-success' : order.status === 'shipping' ? 'bg-primary' : 'bg-warning'}">
                    ${order.statusText}
                </span>
            </div>
            
            <div class="d-flex align-items-center mb-3">
                ${order.items.map(item => `
                    <img src="${item.image}" class="me-2 rounded" alt="${item.name}" style="width: 50px; height: 40px;">
                `).join('')}
                <div class="ms-2">
                    ${order.items.map(item => `
                        <div class="small">${item.name} x${item.quantity}</div>
                    `).join('')}
                </div>
            </div>
            
            <div class="d-flex justify-content-between align-items-center">
                <div class="fw-bold text-success">${order.total}</div>
                <div>
                    <button class="btn btn-outline-success btn-sm me-2">Xem chi tiết</button>
                    ${order.status === 'completed' ? '<button class="btn btn-success btn-sm">Mua lại</button>' : ''}
                </div>
            </div>
        </div>
    `).join('')
}

function createFavoriteProducts() {
    const favorites = [
        { name: 'Rau cải xanh hữu cơ', price: '25,000đ', image: 'https://via.placeholder.com/150x120/28a745/ffffff?text=RC' },
        { name: 'Cá thu tươi', price: '180,000đ', image: 'https://via.placeholder.com/150x120/17a2b8/ffffff?text=CT' },
        { name: 'Tôm sú tươi', price: '350,000đ', image: 'https://via.placeholder.com/150x120/ffc107/ffffff?text=TS' }
    ]
    
    return favorites.map(product => `
        <div class="col-md-4">
            <div class="card">
                <img src="${product.image}" class="card-img-top" alt="${product.name}">
                <div class="card-body">
                    <h6 class="card-title">${product.name}</h6>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-success fw-bold">${product.price}</span>
                        <div>
                            <button class="btn btn-success btn-sm me-1">
                                <i class="bi bi-cart-plus"></i>
                            </button>
                            <button class="btn btn-outline-danger btn-sm">
                                <i class="bi bi-heart-fill"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `).join('')
}

function createAddressList() {
    const addresses = [
        {
            name: 'Nhà riêng',
            address: '123 Đường ABC, Phường Bến Nghé, Quận 1, TP.HCM',
            phone: '0901234567',
            isDefault: true
        },
        {
            name: 'Văn phòng',
            address: '456 Đường XYZ, Phường Đa Kao, Quận 1, TP.HCM',
            phone: '0901234567',
            isDefault: false
        }
    ]
    
    return addresses.map(addr => `
        <div class="border rounded p-3 mb-3">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <h6 class="mb-1">
                        ${addr.name}
                        ${addr.isDefault ? '<span class="badge bg-success ms-2">Mặc định</span>' : ''}
                    </h6>
                    <p class="mb-1">${addr.address}</p>
                    <p class="text-muted mb-0">${addr.phone}</p>
                </div>
                <div>
                    <button class="btn btn-outline-primary btn-sm me-1">Sửa</button>
                    <button class="btn btn-outline-danger btn-sm">Xóa</button>
                </div>
            </div>
        </div>
    `).join('')
}

function createNotificationsList() {
    const notifications = [
        {
            title: 'Đơn hàng #DH001235 đang được giao',
            message: 'Đơn hàng của bạn đang trên đường giao. Dự kiến giao trong 2 giờ tới.',
            time: '2 giờ trước',
            read: false
        },
        {
            title: 'Khuyến mãi đặc biệt',
            message: 'Giảm 50% cho tất cả sản phẩm hải sản. Áp dụng đến hết ngày hôm nay.',
            time: '1 ngày trước',
            read: true
        }
    ]
    
    return notifications.map(notif => `
        <div class="d-flex align-items-start p-3 border-bottom ${!notif.read ? 'bg-light' : ''}">
            <div class="flex-grow-1">
                <h6 class="mb-1">${notif.title}</h6>
                <p class="mb-1">${notif.message}</p>
                <small class="text-muted">${notif.time}</small>
            </div>
            ${!notif.read ? '<div class="bg-primary rounded-circle" style="width: 8px; height: 8px;"></div>' : ''}
        </div>
    `).join('')
}

function initializeDashboard() {
    // Tab switching
    document.querySelectorAll('[data-tab]').forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault()
            
            // Remove active class from all tabs and contents
            document.querySelectorAll('[data-tab]').forEach(t => t.classList.remove('active'))
            document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'))
            
            // Add active class to clicked tab
            this.classList.add('active')
            
            // Show corresponding content
            const tabId = this.dataset.tab + '-content'
            document.getElementById(tabId).classList.add('active')
        })
    })
    
    // Add CSS for tab content
    const style = document.createElement('style')
    style.textContent = `
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
    `
    document.head.appendChild(style)
}
