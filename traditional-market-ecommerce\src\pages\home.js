// Home page module
import { createHeader } from '../components/header.js'
import { createFooter } from '../components/footer.js'

export function initHomePage() {
    // Initialize home page specific functionality
    console.log('Home page initialized')
}

export function renderHomePage() {
    const app = document.querySelector('#app')
    
    app.innerHTML = `
        ${createHeader()}
        
        <!-- Hero Banner -->
        <section class="hero-banner bg-primary text-white py-5">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <h1 class="display-4 fw-bold mb-3">Chợ Truyền Thống Online</h1>
                        <p class="lead mb-4"><PERSON><PERSON><PERSON>h – Gi<PERSON> tốt – Mua tại chợ truyền thống!</p>
                        <div class="d-flex gap-3">
                            <button class="btn btn-warning btn-lg">
                                <i class="bi bi-download me-2"></i>Tải <PERSON>ng dụng
                            </button>
                            <button class="btn btn-outline-light btn-lg" data-route="/merchant-register">
                                <i class="bi bi-shop me-2"></i>Đăng ký bán hàng
                            </button>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <img src="/images/hero-market.jpg" alt="Chợ truyền thống" class="img-fluid rounded" 
                             onerror="this.src='https://via.placeholder.com/600x400/28a745/ffffff?text=Chợ+Truyền+Thống'">
                    </div>
                </div>
            </div>
        </section>

        <!-- Categories Section -->
        <section class="categories py-5">
            <div class="container">
                <h2 class="text-center mb-5">Danh mục nổi bật</h2>
                <div class="row g-4">
                    ${createCategoryCards()}
                </div>
            </div>
        </section>

        <!-- Live Stream Section -->
        <section class="livestream bg-light py-5">
            <div class="container">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>Livestream đang diễn ra</h2>
                    <a href="/livestream" class="btn btn-outline-primary" data-route="/livestream">
                        Xem tất cả <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
                <div class="row g-4">
                    ${createLivestreamCards()}
                </div>
            </div>
        </section>

        <!-- Featured Shops Section -->
        <section class="featured-shops py-5">
            <div class="container">
                <h2 class="text-center mb-5">Gian hàng tiêu biểu</h2>
                <div class="row g-4">
                    ${createShopCards()}
                </div>
            </div>
        </section>

        <!-- Promotions Section -->
        <section class="promotions bg-warning bg-opacity-10 py-5">
            <div class="container">
                <h2 class="text-center mb-5">Ưu đãi hôm nay</h2>
                <div class="row g-4">
                    ${createPromotionCards()}
                </div>
            </div>
        </section>

        ${createFooter()}
    `
    
    // Initialize interactive elements
    initializeInteractivity()
}

function createCategoryCards() {
    const categories = [
        { name: 'Rau củ quả', icon: 'bi-carrot', color: 'success', count: '1,234' },
        { name: 'Hải sản tươi', icon: 'bi-fish', color: 'info', count: '567' },
        { name: 'Thịt gia cầm', icon: 'bi-egg', color: 'warning', count: '890' },
        { name: 'Đồ khô', icon: 'bi-box', color: 'secondary', count: '456' },
        { name: 'Gia vị', icon: 'bi-cup-hot', color: 'danger', count: '234' },
        { name: 'Đặc sản', icon: 'bi-star', color: 'primary', count: '123' }
    ]
    
    return categories.map(cat => `
        <div class="col-lg-2 col-md-4 col-6">
            <div class="card h-100 text-center border-0 shadow-sm category-card" data-route="/categories?type=${cat.name}">
                <div class="card-body">
                    <div class="bg-${cat.color} bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 60px; height: 60px;">
                        <i class="bi ${cat.icon} fs-3 text-${cat.color}"></i>
                    </div>
                    <h6 class="card-title">${cat.name}</h6>
                    <small class="text-muted">${cat.count} sản phẩm</small>
                </div>
            </div>
        </div>
    `).join('')
}

function createLivestreamCards() {
    const livestreams = [
        {
            title: 'Hải sản tươi sống - Chợ Bến Thành',
            viewers: '1,234',
            merchant: 'Cô Lan Hải Sản',
            thumbnail: 'https://via.placeholder.com/300x200/dc3545/ffffff?text=LIVE'
        },
        {
            title: 'Rau củ organic - Chợ Tân Định',
            viewers: '567',
            merchant: 'Anh Minh Rau Sạch',
            thumbnail: 'https://via.placeholder.com/300x200/28a745/ffffff?text=LIVE'
        },
        {
            title: 'Trái cây nhập khẩu - Chợ Đồng Xuân',
            viewers: '890',
            merchant: 'Chị Hoa Trái Cây',
            thumbnail: 'https://via.placeholder.com/300x200/ffc107/ffffff?text=LIVE'
        }
    ]
    
    return livestreams.map(stream => `
        <div class="col-lg-4 col-md-6">
            <div class="card livestream-card">
                <div class="position-relative">
                    <img src="${stream.thumbnail}" class="card-img-top" alt="${stream.title}">
                    <span class="badge bg-danger position-absolute top-0 start-0 m-2">
                        <i class="bi bi-broadcast"></i> LIVE
                    </span>
                    <span class="badge bg-dark bg-opacity-75 position-absolute bottom-0 end-0 m-2">
                        <i class="bi bi-eye"></i> ${stream.viewers}
                    </span>
                </div>
                <div class="card-body">
                    <h6 class="card-title">${stream.title}</h6>
                    <p class="card-text text-muted mb-2">
                        <i class="bi bi-shop me-1"></i>${stream.merchant}
                    </p>
                    <button class="btn btn-primary btn-sm">Xem ngay</button>
                </div>
            </div>
        </div>
    `).join('')
}

function createShopCards() {
    const shops = [
        {
            name: 'Gian hàng Cô Lan',
            market: 'Chợ Bến Thành',
            rating: 4.8,
            products: 156,
            avatar: 'https://via.placeholder.com/80x80/6c757d/ffffff?text=CL'
        },
        {
            name: 'Anh Minh Rau Sạch',
            market: 'Chợ Tân Định',
            rating: 4.9,
            products: 89,
            avatar: 'https://via.placeholder.com/80x80/28a745/ffffff?text=AM'
        },
        {
            name: 'Chị Hoa Trái Cây',
            market: 'Chợ Đồng Xuân',
            rating: 4.7,
            products: 234,
            avatar: 'https://via.placeholder.com/80x80/ffc107/ffffff?text=CH'
        },
        {
            name: 'Bác Tám Hải Sản',
            market: 'Chợ Cầu Mống',
            rating: 4.6,
            products: 67,
            avatar: 'https://via.placeholder.com/80x80/dc3545/ffffff?text=BT'
        }
    ]
    
    return shops.map(shop => `
        <div class="col-lg-3 col-md-6">
            <div class="card shop-card h-100">
                <div class="card-body text-center">
                    <img src="${shop.avatar}" class="rounded-circle mb-3" alt="${shop.name}">
                    <h6 class="card-title">${shop.name}</h6>
                    <p class="text-muted mb-2">
                        <i class="bi bi-geo-alt me-1"></i>${shop.market}
                    </p>
                    <div class="d-flex justify-content-center align-items-center mb-2">
                        <span class="text-warning me-1">
                            ${'★'.repeat(Math.floor(shop.rating))}
                        </span>
                        <span class="text-muted">${shop.rating}</span>
                    </div>
                    <p class="text-muted mb-3">${shop.products} sản phẩm</p>
                    <button class="btn btn-outline-primary btn-sm" data-route="/shop?id=${shop.name}">
                        Xem gian hàng
                    </button>
                </div>
            </div>
        </div>
    `).join('')
}

function createPromotionCards() {
    const promotions = [
        {
            title: 'Giảm 50K đơn từ 200K',
            description: 'Áp dụng cho đơn hàng đầu tiên',
            code: 'WELCOME50',
            color: 'danger'
        },
        {
            title: 'Freeship nội khu',
            description: 'Miễn phí giao hàng trong bán kính 3km',
            code: 'FREESHIP',
            color: 'success'
        },
        {
            title: 'Livestream 0đ',
            description: 'Xem livestream nhận voucher',
            code: 'LIVE0D',
            color: 'primary'
        }
    ]
    
    return promotions.map(promo => `
        <div class="col-lg-4 col-md-6">
            <div class="card border-${promo.color} promotion-card">
                <div class="card-body">
                    <h6 class="card-title text-${promo.color}">${promo.title}</h6>
                    <p class="card-text">${promo.description}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <code class="bg-light px-2 py-1 rounded">${promo.code}</code>
                        <button class="btn btn-${promo.color} btn-sm">Sao chép</button>
                    </div>
                </div>
            </div>
        </div>
    `).join('')
}

function initializeInteractivity() {
    // Add hover effects for category cards
    document.querySelectorAll('.category-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)'
            this.style.transition = 'transform 0.3s ease'
        })
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)'
        })
    })
    
    // Copy promotion codes
    document.querySelectorAll('.promotion-card button').forEach(btn => {
        btn.addEventListener('click', function() {
            const code = this.parentElement.querySelector('code').textContent
            navigator.clipboard.writeText(code).then(() => {
                this.textContent = 'Đã sao chép!'
                setTimeout(() => {
                    this.textContent = 'Sao chép'
                }, 2000)
            })
        })
    })
}
