// Community page module
import { createHeader } from '../components/header.js'
import { createFooter } from '../components/footer.js'

export function renderCommunityPage() {
    const app = document.querySelector('#app')
    
    app.innerHTML = `
        ${createHeader()}
        
        <!-- Community Page -->
        <div class="container py-4">
            <!-- Page Header -->
            <div class="text-center mb-5">
                <h1 class="display-5 fw-bold text-success mb-3">
                    <i class="bi bi-people me-3"></i>Cộng đồng ChợOnline
                </h1>
                <p class="lead text-muted">
                    Kết nối cộng đồng yêu thích mua sắm tại chợ truyền thống
                </p>
            </div>

            <!-- Market Map -->
            <div class="mb-5">
                <h3 class="mb-4">Bản đồ chợ truyền thống</h3>
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                     style="height: 400px;">
                                    <div class="text-center">
                                        <i class="bi bi-map display-1 text-muted mb-3"></i>
                                        <h5>Bản đồ tương tác</h5>
                                        <p class="text-muted">Khám phá các chợ truyền thống đã số hóa</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <h6 class="mb-3">Chợ đã tham gia</h6>
                                ${createMarketList()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Community Activities -->
            <div class="mb-5">
                <h3 class="mb-4">Hoạt động cộng đồng</h3>
                <div class="row g-4">
                    ${createCommunityActivities()}
                </div>
            </div>

            <!-- Success Stories -->
            <div class="mb-5">
                <h3 class="mb-4">Câu chuyện thành công</h3>
                <div class="row g-4">
                    ${createSuccessStories()}
                </div>
            </div>

            <!-- Blog & News -->
            <div class="mb-5">
                <h3 class="mb-4">Tin tức & Blog</h3>
                <div class="row g-4">
                    ${createBlogPosts()}
                </div>
            </div>

            <!-- Community Stats -->
            <div class="mb-5">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <h3 class="text-center mb-4">Cộng đồng ChợOnline</h3>
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="display-6 fw-bold">50+</div>
                                <div>Chợ tham gia</div>
                            </div>
                            <div class="col-md-3">
                                <div class="display-6 fw-bold">2,500+</div>
                                <div>Tiểu thương</div>
                            </div>
                            <div class="col-md-3">
                                <div class="display-6 fw-bold">100K+</div>
                                <div>Khách hàng</div>
                            </div>
                            <div class="col-md-3">
                                <div class="display-6 fw-bold">1M+</div>
                                <div>Đơn hàng</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        ${createFooter()}
    `
}

function createMarketList() {
    const markets = [
        { name: 'Chợ Bến Thành', merchants: 156, status: 'active' },
        { name: 'Chợ Tân Định', merchants: 89, status: 'active' },
        { name: 'Chợ Đồng Xuân', merchants: 234, status: 'active' },
        { name: 'Chợ Cầu Mống', merchants: 67, status: 'active' },
        { name: 'Chợ Hòa Hưng', merchants: 45, status: 'coming' }
    ]
    
    return markets.map(market => `
        <div class="d-flex justify-content-between align-items-center mb-3 p-3 border rounded">
            <div>
                <h6 class="mb-1">${market.name}</h6>
                <small class="text-muted">${market.merchants} tiểu thương</small>
            </div>
            <span class="badge ${market.status === 'active' ? 'bg-success' : 'bg-warning'}">
                ${market.status === 'active' ? 'Hoạt động' : 'Sắp ra mắt'}
            </span>
        </div>
    `).join('')
}

function createCommunityActivities() {
    const activities = [
        {
            title: 'Hỗ trợ số hóa chợ Bình Tây',
            description: 'Đội ngũ ChợOnline đã hỗ trợ 200+ tiểu thương tại chợ Bình Tây',
            image: 'https://via.placeholder.com/300x200/28a745/ffffff?text=Hỗ+Trợ+Chợ',
            date: '15/06/2024'
        },
        {
            title: 'Workshop "Bán hàng online hiệu quả"',
            description: 'Tập huấn cho 150 tiểu thương về kỹ năng bán hàng trực tuyến',
            image: 'https://via.placeholder.com/300x200/17a2b8/ffffff?text=Workshop',
            date: '10/06/2024'
        },
        {
            title: 'Lễ hội ẩm thực chợ truyền thống',
            description: 'Kết nối 50+ gian hàng trong sự kiện ẩm thực lớn nhất năm',
            image: 'https://via.placeholder.com/300x200/ffc107/ffffff?text=Lễ+Hội',
            date: '05/06/2024'
        }
    ]
    
    return activities.map(activity => `
        <div class="col-lg-4 col-md-6">
            <div class="card activity-card h-100">
                <img src="${activity.image}" class="card-img-top" alt="${activity.title}">
                <div class="card-body">
                    <h6 class="card-title">${activity.title}</h6>
                    <p class="card-text">${activity.description}</p>
                    <small class="text-muted">
                        <i class="bi bi-calendar me-1"></i>${activity.date}
                    </small>
                </div>
            </div>
        </div>
    `).join('')
}

function createSuccessStories() {
    const stories = [
        {
            name: 'Cô Lan - Chợ Bến Thành',
            story: 'Từ việc chỉ bán tại chợ, giờ tôi có thể phục vụ khách hàng toàn thành phố. Doanh thu tăng 300% sau 6 tháng.',
            avatar: 'https://via.placeholder.com/80x80/28a745/ffffff?text=CL',
            revenue: '+300%',
            period: '6 tháng'
        },
        {
            name: 'Anh Minh - Chợ Tân Định',
            story: 'Livestream giúp tôi tương tác trực tiếp với khách hàng. Mỗi buổi live bán được 50-100 đơn hàng.',
            avatar: 'https://via.placeholder.com/80x80/17a2b8/ffffff?text=AM',
            revenue: '+250%',
            period: '4 tháng'
        },
        {
            name: 'Chị Hoa - Chợ Đồng Xuân',
            story: 'Nhờ ChợOnline, tôi đã mở rộng được sang bán đồ khô đặc sản. Khách hàng từ khắp nơi đặt hàng.',
            avatar: 'https://via.placeholder.com/80x80/ffc107/ffffff?text=CH',
            revenue: '+400%',
            period: '8 tháng'
        }
    ]
    
    return stories.map(story => `
        <div class="col-lg-4 col-md-6">
            <div class="card success-story-card h-100">
                <div class="card-body text-center">
                    <img src="${story.avatar}" class="rounded-circle mb-3" alt="${story.name}">
                    <h6 class="card-title">${story.name}</h6>
                    <p class="card-text fst-italic">"${story.story}"</p>
                    <div class="row text-center mt-3">
                        <div class="col-6">
                            <div class="fw-bold text-success">${story.revenue}</div>
                            <small class="text-muted">Doanh thu</small>
                        </div>
                        <div class="col-6">
                            <div class="fw-bold text-success">${story.period}</div>
                            <small class="text-muted">Thời gian</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `).join('')
}

function createBlogPosts() {
    const posts = [
        {
            title: '10 bí quyết chọn rau củ tươi ngon tại chợ',
            excerpt: 'Hướng dẫn chi tiết cách nhận biết rau củ tươi, an toàn cho sức khỏe...',
            author: 'Đội ngũ ChợOnline',
            date: '20/06/2024',
            image: 'https://via.placeholder.com/300x200/28a745/ffffff?text=Rau+Củ+Tips',
            category: 'Mẹo hay'
        },
        {
            title: 'Xu hướng mua sắm online tại chợ truyền thống',
            excerpt: 'Phân tích xu hướng và thói quen mua sắm của người tiêu dùng hiện đại...',
            author: 'Phòng Marketing',
            date: '18/06/2024',
            image: 'https://via.placeholder.com/300x200/17a2b8/ffffff?text=Xu+Hướng',
            category: 'Thị trường'
        },
        {
            title: 'Câu chuyện về sự phát triển của chợ Việt Nam',
            excerpt: 'Từ chợ truyền thống đến chợ số - hành trình chuyển đổi của các chợ...',
            author: 'Ban Biên Tập',
            date: '15/06/2024',
            image: 'https://via.placeholder.com/300x200/ffc107/ffffff?text=Lịch+Sử',
            category: 'Văn hóa'
        }
    ]
    
    return posts.map(post => `
        <div class="col-lg-4 col-md-6">
            <div class="card blog-post-card h-100">
                <img src="${post.image}" class="card-img-top" alt="${post.title}">
                <div class="card-body">
                    <span class="badge bg-success mb-2">${post.category}</span>
                    <h6 class="card-title">${post.title}</h6>
                    <p class="card-text">${post.excerpt}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="bi bi-person me-1"></i>${post.author}
                        </small>
                        <small class="text-muted">
                            <i class="bi bi-calendar me-1"></i>${post.date}
                        </small>
                    </div>
                </div>
                <div class="card-footer bg-transparent">
                    <button class="btn btn-outline-success btn-sm">
                        Đọc thêm <i class="bi bi-arrow-right"></i>
                    </button>
                </div>
            </div>
        </div>
    `).join('')
}
