// Merchant registration page module
import { createHeader } from '../components/header.js'
import { createFooter } from '../components/footer.js'

export function renderMerchantRegisterPage() {
    const app = document.querySelector('#app')
    
    app.innerHTML = `
        ${createHeader()}
        
        <!-- Merchant Registration Page -->
        <div class="container py-4">
            <!-- Hero Section -->
            <div class="text-center mb-5">
                <h1 class="display-5 fw-bold text-success mb-3">
                    Đăng ký bán hàng cùng ChợOnline
                </h1>
                <p class="lead text-muted">
                    Mở rộng kinh doanh, tiếp cận hàng triệu khách hàng trực tuyến
                </p>
            </div>

            <!-- Benefits Section -->
            <div class="row mb-5">
                <div class="col-lg-4 text-center mb-4">
                    <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 80px; height: 80px;">
                        <i class="bi bi-graph-up fs-2 text-success"></i>
                    </div>
                    <h5>Tăng doanh thu</h5>
                    <p class="text-muted">Tiếp cận khách hàng rộng khắp, bán hàng 24/7</p>
                </div>
                <div class="col-lg-4 text-center mb-4">
                    <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 80px; height: 80px;">
                        <i class="bi bi-broadcast fs-2 text-success"></i>
                    </div>
                    <h5>Livestream miễn phí</h5>
                    <p class="text-muted">Tương tác trực tiếp, tăng tỷ lệ chuyển đổi</p>
                </div>
                <div class="col-lg-4 text-center mb-4">
                    <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 80px; height: 80px;">
                        <i class="bi bi-headset fs-2 text-success"></i>
                    </div>
                    <h5>Hỗ trợ 24/7</h5>
                    <p class="text-muted">Đội ngũ hỗ trợ chuyên nghiệp, tận tình</p>
                </div>
            </div>

            <!-- Registration Form -->
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card shadow">
                        <div class="card-header bg-success text-white">
                            <h4 class="mb-0">
                                <i class="bi bi-shop me-2"></i>
                                Thông tin đăng ký
                            </h4>
                        </div>
                        <div class="card-body">
                            <form id="merchantRegisterForm">
                                <!-- Personal Information -->
                                <h5 class="mb-3">Thông tin cá nhân</h5>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">Họ và tên *</label>
                                        <input type="text" class="form-control" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Số điện thoại *</label>
                                        <input type="tel" class="form-control" required>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">Email</label>
                                        <input type="email" class="form-control">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">CCCD/CMND *</label>
                                        <input type="text" class="form-control" required>
                                    </div>
                                </div>

                                <hr class="my-4">

                                <!-- Shop Information -->
                                <h5 class="mb-3">Thông tin gian hàng</h5>
                                <div class="mb-3">
                                    <label class="form-label">Tên gian hàng *</label>
                                    <input type="text" class="form-control" required>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">Chợ bán hàng *</label>
                                        <select class="form-select" required>
                                            <option value="">Chọn chợ</option>
                                            <option>Chợ Bến Thành</option>
                                            <option>Chợ Tân Định</option>
                                            <option>Chợ Đồng Xuân</option>
                                            <option>Chợ Cầu Mống</option>
                                            <option>Chợ Hòa Hưng</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Số sạp/vị trí</label>
                                        <input type="text" class="form-control">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Loại mặt hàng *</label>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="rau-cu">
                                                <label class="form-check-label" for="rau-cu">Rau củ quả</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="hai-san">
                                                <label class="form-check-label" for="hai-san">Hải sản</label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="thit">
                                                <label class="form-check-label" for="thit">Thịt gia cầm</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="do-kho">
                                                <label class="form-check-label" for="do-kho">Đồ khô</label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="gia-vi">
                                                <label class="form-check-label" for="gia-vi">Gia vị</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="dac-san">
                                                <label class="form-check-label" for="dac-san">Đặc sản</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Mô tả gian hàng</label>
                                    <textarea class="form-control" rows="3" 
                                              placeholder="Giới thiệu về gian hàng, kinh nghiệm bán hàng..."></textarea>
                                </div>

                                <hr class="my-4">

                                <!-- Package Selection -->
                                <h5 class="mb-3">Chọn gói dịch vụ</h5>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="card package-card" data-package="basic">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">Gói Cơ Bản</h6>
                                                <div class="display-6 text-success mb-2">Miễn phí</div>
                                                <ul class="list-unstyled text-start">
                                                    <li><i class="bi bi-check text-success me-2"></i>Đăng sản phẩm</li>
                                                    <li><i class="bi bi-check text-success me-2"></i>Quản lý đơn hàng</li>
                                                    <li><i class="bi bi-check text-success me-2"></i>Hỗ trợ cơ bản</li>
                                                </ul>
                                                <button type="button" class="btn btn-outline-success w-100">Chọn gói</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card package-card border-success" data-package="premium">
                                            <div class="card-body text-center">
                                                <span class="badge bg-success position-absolute top-0 start-50 translate-middle">
                                                    Phổ biến
                                                </span>
                                                <h6 class="card-title">Gói Premium</h6>
                                                <div class="display-6 text-success mb-2">299K<small>/tháng</small></div>
                                                <ul class="list-unstyled text-start">
                                                    <li><i class="bi bi-check text-success me-2"></i>Tất cả tính năng cơ bản</li>
                                                    <li><i class="bi bi-check text-success me-2"></i>Livestream không giới hạn</li>
                                                    <li><i class="bi bi-check text-success me-2"></i>Ưu tiên hiển thị</li>
                                                    <li><i class="bi bi-check text-success me-2"></i>Thống kê chi tiết</li>
                                                </ul>
                                                <button type="button" class="btn btn-success w-100">Chọn gói</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card package-card" data-package="enterprise">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">Gói Doanh Nghiệp</h6>
                                                <div class="display-6 text-success mb-2">599K<small>/tháng</small></div>
                                                <ul class="list-unstyled text-start">
                                                    <li><i class="bi bi-check text-success me-2"></i>Tất cả tính năng Premium</li>
                                                    <li><i class="bi bi-check text-success me-2"></i>Quảng cáo banner</li>
                                                    <li><i class="bi bi-check text-success me-2"></i>Hỗ trợ 24/7</li>
                                                    <li><i class="bi bi-check text-success me-2"></i>Đào tạo chuyên sâu</li>
                                                </ul>
                                                <button type="button" class="btn btn-outline-success w-100">Chọn gói</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <hr class="my-4">

                                <!-- Terms and Submit -->
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="terms" required>
                                        <label class="form-check-label" for="terms">
                                            Tôi đồng ý với 
                                            <a href="#" class="text-success">Điều khoản sử dụng</a> và 
                                            <a href="#" class="text-success">Chính sách bảo mật</a>
                                        </label>
                                    </div>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn btn-success btn-lg">
                                        <i class="bi bi-check-circle me-2"></i>
                                        Đăng ký ngay
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Support Section -->
            <div class="text-center mt-5">
                <h5>Cần hỗ trợ?</h5>
                <p class="text-muted mb-3">
                    Liên hệ với chúng tôi để được tư vấn miễn phí
                </p>
                <div class="d-flex justify-content-center gap-3">
                    <a href="tel:1900-1234" class="btn btn-outline-success">
                        <i class="bi bi-telephone me-1"></i>1900-1234
                    </a>
                    <a href="#" class="btn btn-outline-success">
                        <i class="bi bi-chat-dots me-1"></i>Chat với chúng tôi
                    </a>
                </div>
            </div>
        </div>

        ${createFooter()}
    `
    
    initializeMerchantRegister()
}

function initializeMerchantRegister() {
    // Package selection
    document.querySelectorAll('.package-card').forEach(card => {
        card.addEventListener('click', function() {
            // Remove active class from all cards
            document.querySelectorAll('.package-card').forEach(c => {
                c.classList.remove('border-success', 'bg-light')
                c.querySelector('button').classList.remove('btn-success')
                c.querySelector('button').classList.add('btn-outline-success')
                c.querySelector('button').textContent = 'Chọn gói'
            })
            
            // Add active class to clicked card
            this.classList.add('border-success', 'bg-light')
            const button = this.querySelector('button')
            button.classList.remove('btn-outline-success')
            button.classList.add('btn-success')
            button.textContent = 'Đã chọn'
        })
    })
    
    // Form submission
    document.getElementById('merchantRegisterForm').addEventListener('submit', function(e) {
        e.preventDefault()
        
        // Show success message
        const submitBtn = this.querySelector('button[type="submit"]')
        const originalText = submitBtn.innerHTML
        
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Đang xử lý...'
        submitBtn.disabled = true
        
        setTimeout(() => {
            alert('Đăng ký thành công! Chúng tôi sẽ liên hệ với bạn trong 24h.')
            submitBtn.innerHTML = originalText
            submitBtn.disabled = false
        }, 2000)
    })
}
